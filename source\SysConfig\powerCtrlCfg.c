#include "includes.h"
#include "PowerCtrlDrv.h"

const uint8_t powerCtrlBatDetectAdcNum = BAT_DET_ADC_CHANNEL;
const uint8_t powerCtrlChgDetPinId = BAT_DET_CHG_DET_PIN;
const uint8_t powerCtrlChgPgPinId = BAT_DET_CHG_PG_PIN;

const powerCtrlSwitchClass_t powerCtrlSwitchTable[] = 
{
	// {
	// 	.pinId = NRF_GPIO_PIN_MAP(0, 30),
	// 	.controller = powerCtrlSwitchControllerByMCU,
	// 	.activeLevel = POWERCTRL_SWITCH_ACTIVE_LEVEL_HIGH,
	// 	.initLevel = POWERCTRL_SWITCH_ACTIVE_LEVEL_HIGH,
	// 	.flag = (POWERCTRL_SWITCH_FLAG_TURNON_BY_POWERON | POWERCTRL_SWITCH_FLAG_TURNOFF_BY_POWEROFF),
	// 	.name = "PWR_HOLD",
	// 	.userPtr = NULL,
	// 	//.userFunc = NULL
	// },
	{
		.pinId = NRF_GPIO_PIN_MAP(0, 31),
		.controller = powerCtrlSwitchControllerByMCU,
		.activeLevel = POWERCTRL_SWITCH_ACTIVE_LEVEL_HIGH,
		.initLevel = POWERCTRL_SWITCH_ACTIVE_LEVEL_LOW,
		.flag = (POWERCTRL_SWITCH_FLAG_TURNON_BY_POWERON  | POWERCTRL_SWITCH_FLAG_TURNOFF_BY_POWEROFF),
		.name = "PWR_GUN",
		.userPtr = NULL,
		//.userFunc = NULL
	},
//	{
//        .pinId = PWR_CTRL_PIN_HALL_POWER,
//		.controller = powerCtrlSwitchControllerByMCU,
//		.activeLevel = POWERCTRL_SWITCH_ACTIVE_LEVEL_LOW,
//		.initLevel = POWERCTRL_SWITCH_ACTIVE_LEVEL_HIGH,
//		.flag = (0),
//		.name = "HALL_PWR",
//		.userPtr = NULL,
//		//.userFunc = NULL
//	},
	// {
    //     .pinId = SYS_CFG_PIN_CTRL_POWER_CHG_CE,
	// 	.controller = powerCtrlSwitchControllerByMCU,
	// 	.activeLevel = POWERCTRL_SWITCH_ACTIVE_LEVEL_HIGH,
	// 	.initLevel = POWERCTRL_SWITCH_ACTIVE_LEVEL_LOW,
	// 	.flag = (0),
	// 	.name = "CHG_CE", 
	// 	.userPtr = NULL,
	// 	//.userFunc = NULL
	// },
	// {
    //     .pinId = SYS_CFG_PIN_CTRL_POWER_DCDC_PS,
	// 	.controller = powerCtrlSwitchControllerByMCU,
	// 	.activeLevel = POWERCTRL_SWITCH_ACTIVE_LEVEL_LOW,
	// 	.initLevel = POWERCTRL_SWITCH_ACTIVE_LEVEL_LOW,
	// 	.flag = (POWERCTRL_SWITCH_FLAG_TURNON_BY_POWERON),
	// 	.name = "DCDC_SAVEPOWER",
	// 	.userPtr = NULL,
	// 	//.userFunc = NULL
	// },
};

const uint8_t powerCtrlSwitchTableNum = sizeof(powerCtrlSwitchTable) / sizeof(powerCtrlSwitchClass_t);
