/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : LedCtrlDrv.c
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/06/29    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef LED_CTRL_DRV_C
#define LED_CTRL_DRV_C
#endif

/* include files */
#include "LedCtrlDrv.h"

/* static variable definition */
static uint16_t     gLedFlashCnt[LED_CTRL_NUM];
static uint16_t     gLedFlashPeriod[LED_CTRL_NUM];
static uint8_t      gLedFlashDuty[LED_CTRL_NUM];
static uint8_t      gLedOutModel[LED_CTRL_NUM];
/* static function declaration */


/* static function definition */

/* public function definition */
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name : LedPwmTimInit
* Description   : 
*
* Inputs        : @param   :      
*                 @param   aPeriod:    max 100Hz  flash unit 10ms  counter
*
* Outputs       : @param   xxxx:
*                          
*                 @global val  xxxx:
*
*                 @retval :
*                               
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
void LedFlashLighting(uint8_t aLedSelect,uint32_t aPeriod,uint32_t aDuty)
{
    if(aLedSelect < LED_CTRL_NUM 
		&& (gLedOutModel[aLedSelect] != LED_CTRL_MODEL_FLASH || aPeriod != gLedFlashPeriod[aLedSelect] || gLedFlashDuty[aLedSelect] != aDuty))
    {
		DEBUG_PRINTF("led flash:%d, per:%d, duty:%d", aLedSelect, aPeriod, aDuty);
        if(gLedOutModel[aLedSelect] == LED_CTRL_MODEL_BREATH)
        {
            led_softblink_stop();
            led_softblink_uninit();
        }
        else if (gLedOutModel[aLedSelect] == LED_CTRL_MODEL_PWM)
        {
            LedPwmPwrCtrl(0,0,0,0,0);
        }
        for(uint8_t i =0; i < LED_CTRL_NUM;i++)
        {
            if(gLedOutModel[i] == LED_CTRL_MODEL_BREATH)
            {
                 LedBreathLigting(i);
            }
            else if(gLedOutModel[i] == LED_CTRL_MODEL_NOR)
            {
                LED_CTRL_NOR_ON(gcLedHwCfg_t[aLedSelect].m_PortAddr,gcLedHwCfg_t[aLedSelect].m_PinOffset);
            }
        }
        if(aDuty >= aPeriod)
        {
            aDuty -= 1u;
        }
        gLedFlashPeriod[aLedSelect] =  aPeriod;
        gLedFlashDuty[aLedSelect]   =  aDuty;
        if(gLedOutModel[aLedSelect] != LED_CTRL_MODEL_FLASH)
        {
            gLedFlashCnt[aLedSelect] = 0u;
            LED_CTRL_NOR_ON(gcLedHwCfg_t[aLedSelect].m_PortAddr,gcLedHwCfg_t[aLedSelect].m_PinOffset);         
            if(aLedSelect == 0x00 || aLedSelect == 0x01)
            {
                if(gLedFlashCnt[!aLedSelect] < gLedFlashDuty[!aLedSelect])
                {
                    gLedFlashCnt[aLedSelect] = aDuty;
                    LED_CTRL_NOR_OFF(gcLedHwCfg_t[aLedSelect].m_PortAddr,gcLedHwCfg_t[aLedSelect].m_PinOffset);      
                }
            }   
           

        }
        gLedOutModel[aLedSelect] = LED_CTRL_MODEL_FLASH;
    }
    else
    {
        //do nothing
    }
}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name : LedPwmStart
* Description   : enable pwm
*
* Inputs        : @param   aLedSelect:  
*                 @param   
* Outputs       : @param   xxxx:
*                          
*                 @global val  xxxx:
*
*                 @retval : register value
*                               
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
void LedBreathLigting(uint8_t aLedSelect)
{ 
    ret_code_t err_code;
    led_sb_init_params_t led_sb_init_param =LED_SB_INIT_DEFAULT_PARAMS(0x00000000);
    uint32_t LedPinmask = 0;
    if((aLedSelect < LED_CTRL_NUM) && (gLedOutModel[aLedSelect] != LED_CTRL_MODEL_BREATH))
    {
        for(uint8_t i =0; i < LED_CTRL_NUM;i++)
        {
            if(gLedOutModel[i] == LED_CTRL_MODEL_BREATH)
            {
                 led_softblink_stop();
                 led_softblink_uninit();
                 LedPinmask |= PIN_MASK(gcLedHwCfg_t[i].m_PinOffset);
            }
        }
        if(gLedOutModel[aLedSelect] == LED_CTRL_MODEL_FLASH )
        {
            gLedOutModel[aLedSelect] = LED_CTRL_MODEL_NOR;
            gLedFlashCnt[aLedSelect] = 0u;
            LED_CTRL_NOR_OFF(gcLedHwCfg_t[aLedSelect].m_PortAddr,gcLedHwCfg_t[aLedSelect].m_PinOffset);
        }
        else if (gLedOutModel[aLedSelect] == LED_CTRL_MODEL_PWM)
        {
            LedPwmPwrCtrl(0,0,0,0,0);
        }
        
        LedPinmask |= PIN_MASK(gcLedHwCfg_t[aLedSelect].m_PinOffset);
        gLedFlashPeriod[aLedSelect] =  0;
        gLedFlashDuty[aLedSelect]   =  0;
        gLedOutModel[aLedSelect] = LED_CTRL_MODEL_BREATH;
        LED_CTRL_NOR_OFF(gcLedHwCfg_t[aLedSelect].m_PortAddr,gcLedHwCfg_t[aLedSelect].m_PinOffset);
        
        led_sb_init_param.active_high        = false;
        led_sb_init_param.duty_cycle_max     = 252;
        led_sb_init_param.duty_cycle_min     = 0;
        led_sb_init_param.duty_cycle_step    = 4;
        led_sb_init_param.off_time_ticks     = 10;
        led_sb_init_param.on_time_ticks      = 10;
        led_sb_init_param.leds_pin_bm        = LedPinmask;
        led_sb_init_param.p_leds_port        = NRF_GPIO;
        err_code = led_softblink_init(&led_sb_init_param);
        APP_ERROR_CHECK(err_code);

        err_code = led_softblink_start(LedPinmask);
        APP_ERROR_CHECK(err_code);
    }
    else
    {
        //do nothing
    }
}
void LedPwmPwrCtrl(uint8_t aEnable,uint16_t preiod,uint16_t duty_R,uint16_t duty_G,uint16_t duty_B)
{
    if(aEnable == 1u)  /*enable*/
    {
        if(gLedOutModel[0] != LED_CTRL_MODEL_PWM)
        {
            for(uint8_t i =0; i < LED_CTRL_NUM;i++)
            {
                if(gLedOutModel[i] == LED_CTRL_MODEL_BREATH)
                {
                     led_softblink_stop();
                     led_softblink_uninit();
                }
                LED_CTRL_NOR_OFF(gcLedHwCfg_t[i].m_PortAddr,gcLedHwCfg_t[i].m_PinOffset);
                gLedFlashPeriod[i] =  0;
                gLedFlashDuty[i]   =  0;
                gLedOutModel[i] = LED_CTRL_MODEL_PWM;
            }

            // SlowPwmTimInit(0,preiod,duty_G,true,PIN_MASK(SYS_CFG_PIN_CTRL_LED_BLUE)|PIN_MASK(SYS_CFG_PIN_CTRL_LED_RED),PIN_PORT_LED(SYS_CFG_PIN_CTRL_LED_RED));
            // SlowPwmStart(0, PIN_MASK(SYS_CFG_PIN_CTRL_LED_BLUE)|PIN_MASK(SYS_CFG_PIN_CTRL_LED_RED));
            SlowPwmTimInit(0,preiod,duty_G,true,PIN_MASK(SYS_CFG_PIN_CTRL_LED_BLUE), PIN_PORT_LED(SYS_CFG_PIN_CTRL_LED_BLUE));
            SlowPwmStart(0, PIN_MASK(SYS_CFG_PIN_CTRL_LED_BLUE));
        }
        else
        {
            SlowPwmSetDuty(0,duty_G);
        }
        NRF_LOG_INFO("LED pwm R-Duty =%d,G-Duty=%d",duty_R,duty_G);
    }
    else
    {
        if(gLedOutModel[0] == LED_CTRL_MODEL_PWM)
        {
            NRF_LOG_INFO("LED pwm stop");
            SlowPwmStop(0);
            for(uint8_t i =0; i < LED_CTRL_NUM;i++)
            {
                LED_CTRL_NOR_OFF(gcLedHwCfg_t[i].m_PortAddr,gcLedHwCfg_t[i].m_PinOffset);
                gLedFlashPeriod[i] =  0;
                gLedFlashDuty[i]   =  0;
                gLedOutModel[i] = LED_CTRL_MODEL_NOR;
            }
        }
    }
}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name : LedLighten
* Description   : 
*
* Inputs        : @param   aLedSelect:  
*                 @param   
* Outputs       : @param   xxxx:
*                          
*                 @global val  xxxx:
*
*                 @retval : register value
*                               
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
void LedLighten(uint8_t aLedSelect)
{ 
	DEBUG_PRINTF("led lighten:%d", aLedSelect);
    if(aLedSelect < LED_CTRL_NUM)
    {
        if(gLedOutModel[aLedSelect] == LED_CTRL_MODEL_NOR)
        {
            LED_CTRL_NOR_ON(gcLedHwCfg_t[aLedSelect].m_PortAddr,gcLedHwCfg_t[aLedSelect].m_PinOffset);
        }
        else if(gLedOutModel[aLedSelect] == LED_CTRL_MODEL_FLASH)
        {
            gLedOutModel[aLedSelect] = LED_CTRL_MODEL_NOR;
            gLedFlashCnt[aLedSelect] = 0u;
            LED_CTRL_NOR_ON(gcLedHwCfg_t[aLedSelect].m_PortAddr,gcLedHwCfg_t[aLedSelect].m_PinOffset);
        }
        else if(gLedOutModel[aLedSelect] == LED_CTRL_MODEL_BREATH)
        {

            led_softblink_stop();
            led_softblink_uninit();
            gLedOutModel[aLedSelect] = LED_CTRL_MODEL_NOR;
            for(uint8_t i =0; i < LED_CTRL_NUM;i++)
            {
                if(gLedOutModel[i] == LED_CTRL_MODEL_BREATH)
                {
                     LedBreathLigting(i);
                }
            }
            LED_CTRL_NOR_ON(gcLedHwCfg_t[aLedSelect].m_PortAddr,gcLedHwCfg_t[aLedSelect].m_PinOffset);
        }
        else if (gLedOutModel[aLedSelect] == LED_CTRL_MODEL_PWM)
        {
            LedPwmPwrCtrl(0,0,0,0,0);
            gLedOutModel[aLedSelect] = LED_CTRL_MODEL_NOR;
            gLedFlashCnt[aLedSelect] = 0u;
            LED_CTRL_NOR_ON(gcLedHwCfg_t[aLedSelect].m_PortAddr,gcLedHwCfg_t[aLedSelect].m_PinOffset);
        }
            
    }
    else
    {
        //do nothing
    }
}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name : LedClose
* Description   : 
*
* Inputs        : @param   aLedSelect:  
*                 @param   
* Outputs       : @param   xxxx:
*                          
*                 @global val  xxxx:
*
*                 @retval : register value
*                               
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
void LedClose(uint8_t aLedSelect)
{ 
    if(aLedSelect < LED_CTRL_NUM)
    {
//		DEBUG_PRINTF("CLOSE LED:%d", aLedSelect);
        if(gLedOutModel[aLedSelect] == LED_CTRL_MODEL_NOR)
        {
            LED_CTRL_NOR_OFF(gcLedHwCfg_t[aLedSelect].m_PortAddr,gcLedHwCfg_t[aLedSelect].m_PinOffset);
        }
        else if(gLedOutModel[aLedSelect] == LED_CTRL_MODEL_FLASH)
        {
            gLedOutModel[aLedSelect] = LED_CTRL_MODEL_NOR;
            gLedFlashCnt[aLedSelect] = 0u;
            LED_CTRL_NOR_OFF(gcLedHwCfg_t[aLedSelect].m_PortAddr,gcLedHwCfg_t[aLedSelect].m_PinOffset);
        }
        else if(gLedOutModel[aLedSelect] == LED_CTRL_MODEL_BREATH)
        {
            led_softblink_stop();
            led_softblink_uninit();
            gLedOutModel[aLedSelect] = LED_CTRL_MODEL_NOR;
            for(uint8_t i =0; i < LED_CTRL_NUM;i++)
            {
                if(gLedOutModel[i] == LED_CTRL_MODEL_BREATH)
                {
                     LedBreathLigting(i);
                }
            }
            LED_CTRL_NOR_OFF(gcLedHwCfg_t[aLedSelect].m_PortAddr,gcLedHwCfg_t[aLedSelect].m_PinOffset);
        }
        else if (gLedOutModel[aLedSelect] == LED_CTRL_MODEL_PWM)
        {
            LedPwmPwrCtrl(0,0,0,0,0);
            gLedOutModel[aLedSelect] = LED_CTRL_MODEL_NOR;
            gLedFlashCnt[aLedSelect] = 0u;
            LED_CTRL_NOR_OFF(gcLedHwCfg_t[aLedSelect].m_PortAddr,gcLedHwCfg_t[aLedSelect].m_PinOffset);
        }
    }
    else
    {
        //do nothing
    }
}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name : LedCtrlFlashCnt
* Description   : 
*
* Inputs        : @param   xxxx:  
*                 @param   
* Outputs       : @param   xxxx:
*                          
*                 @global val  xxxx:
*
*                 @retval : xxxx
*                               
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
void LedCtrlFlashCnt(void)
{
    for(uint8_t i = 0; i < LED_CTRL_NUM; i++)
    {
        if(gLedOutModel[i] == LED_CTRL_MODEL_FLASH)
        {
            gLedFlashCnt[i]++;
            
            if(gLedFlashCnt[i] <= gLedFlashDuty[i])
            {
                LED_CTRL_NOR_ON(gcLedHwCfg_t[i].m_PortAddr,gcLedHwCfg_t[i].m_PinOffset);
            }
            else if(gLedFlashCnt[i] < gLedFlashPeriod[i])
            {
                LED_CTRL_NOR_OFF(gcLedHwCfg_t[i].m_PortAddr,gcLedHwCfg_t[i].m_PinOffset);
            }
            else
            {
                gLedFlashCnt[i] = 0u;
            }
        }
        else
        {
            //do nothing
        }
    }
}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name : LedCtrlModuleInit
* Description   : 
*
* Inputs        : @param   xxxx:  
*                 @param   
* Outputs       : @param   xxxx:
*                          
*                 @global val  xxxx:
*
*                 @retval : xxxx
*                               
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
void LedCtrlModuleInit(void)
{
    memset(gLedFlashCnt,0x00,sizeof(gLedFlashCnt));
    memset(gLedFlashPeriod,0x00,sizeof(gLedFlashPeriod));
    memset(gLedFlashDuty,0x00,sizeof(gLedFlashDuty));
    memset(gLedOutModel,0x00,sizeof(gLedOutModel));
    
}

/***********************************************END**********************************************/
