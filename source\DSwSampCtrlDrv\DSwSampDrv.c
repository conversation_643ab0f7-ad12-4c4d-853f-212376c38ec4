
#include "DSwSampDrv.h"

extern const dSwSampClass_t dSwSampCfg[];
extern const uint8_t dSwSampCfgNum;

extern DSwSampBitStr iDSwSampVar[]; 
extern uint8_t  gDSwLogicSt[];




void DSwSampButtonDataSample(void)
{
    uint8_t i=0;
    for(i=0;i<dSwSampCfgNum;i++)
    {
        uint8_t state = gDSwLogicSt[i];
        dSwSampClass_t *dev = (dSwSampClass_t *)&dSwSampCfg[i];
        switch(dev->type)
        {
            // case dSwSampTypeWKeyButton:
            //     gSystemSt_t.mButtonSt_t.mDSW_t.bits.TouchPad_bit = state;
            //     break;
            case dSwSampTypePowerButton:
                gSystemSt_t.mButtonSt_t.mDSW_t.bits.Power_Home_bit = state;
                break;
            case dSwSampTypeAppButton:
                gSystemSt_t.mButtonSt_t.mDSW_t.bits.App_bit = state;
                break;
			case dSwSampTypeBowHall:
                gSystemSt_t.mButtonSt_t.mDSW_t.bits.finger_tp_check_bit = state;
            break;
            // case dSwSampTypeDigitalMainTigger:
            //     gSystemSt_t.mButtonSt_t.mTrig = state? TRIGGER_MAX_VAL: TRIGGER_MIN_VAL;
            //     break;
            default:
                DEBUG_PRINTF("%s:%d default: %d", __FILE__, __LINE__, dev->type);
			break;
        }
    }
}
