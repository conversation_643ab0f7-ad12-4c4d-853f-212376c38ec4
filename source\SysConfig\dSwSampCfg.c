#include "dSwSampDrv.h"

const dSwSampClass_t dSwSampCfg[] = {
	{
		.pinId = SYS_CFG_PIN_BUTTON_POWER, //NRF_GPIO_PIN_MAP(0, 31),
		.controller = dSwSampControllerTypeMcuGpio,
		.mode = D_SW_SAMP_MODE_DIRECT,
		.cfg = 0, //need setting wakeup on bspInit.
		.type = dSwSampTypePowerButton,
	},
	{
		.pinId = NRF_GPIO_PIN_MAP(0, 29),
		.controller = dSwSampControllerTypeMcuGpio,
		.mode = D_SW_SAMP_MODE_INV,
		.cfg = DSW_SAMP_PULLUP | DSW_SAMP_GPIO_INIT,
		.type = dSwSampTypeAppButton,
	},
};

const uint8_t dSwSampCfgNum = sizeof(dSwSampCfg) / sizeof(dSwSampClass_t);
DSwSampBitStr iDSwSampVar[dSwSampCfgNum]; 
uint8_t gDSwLogicSt[dSwSampCfgNum];

