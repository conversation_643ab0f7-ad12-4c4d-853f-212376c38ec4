/**
 * Copyright (c) 2016 - 2019 Nordic Semiconductor ASA and Luxoft Global Operations Gmbh.
 *
 * All Rights Reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Nordic
 *    Semiconductor ASA integrated circuit in a product or a software update for
 *    such product, must reproduce the above copyright notice, this list of
 *    conditions and the following disclaimer in the documentation and/or other
 *    materials provided with the distribution.
 *
 * 3. Neither the name of Nordic Semiconductor ASA nor the names of its
 *    contributors may be used to endorse or promote products derived from this
 *    software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Nordic Semiconductor ASA integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * 
 * THIS SOFTWARE IS PROVIDED BY NORDIC SEMICONDUCTOR ASA "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL NORDIC SEMICONDUCTOR ASA OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */
#ifndef HAL_MUTEX_H_INCLUDED
#define HAL_MUTEX_H_INCLUDED

#include "hal_atomic.h"

/** @file
 * This is a simple mutex interface to be used in System Memory Manager
 * to make it thread aware.
 *
 * @defgroup hal_mutex HAL Mutex API
 * @ingroup hal_15_4
 * @{
 * @details NRF52 implementation is void and PC implementation is identical to atomic.
 */


#if defined   ( __GNUC__ )
#include <signal.h>
typedef volatile sig_atomic_t mutex_t;
#else
#include <stdint.h>
typedef volatile uint32_t     mutex_t;
#endif


/**@brief Configures mutex lock before first usage.
 *
 * @param[inout] p_mutex  pointer to mutex variable.
 */
void hal_mutex_init(mutex_t * p_mutex);

/**@brief Atomically sets mutex. If set is failed, enters spin lock loop.
 *
 * @param[in] p_mutex  pointer to mutex variable.
 */
void hal_mutex_lock(mutex_t * p_mutex);

/**
 * @brief Atomically clears mutex. Every other thread, spinning at this lock may
 * try to lock it afterwards.
 *
 * @param[in] p_mutex  pointer to mutex variable.
 */
void hal_mutex_unlock(mutex_t * p_mutex);

/** @} */

#endif /* HAL_MUTEX_H_INCLUDED */
