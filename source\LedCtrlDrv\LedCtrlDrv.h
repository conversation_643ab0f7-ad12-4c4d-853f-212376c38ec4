/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : LedCtrlDrv.h
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/06/29    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef LED_CTRL_DRV_H
#define LED_CTRL_DRV_H

/*Include files*/
#include "LedCtrlAppExt.h"

/* support module */
#include "includes.h"

/*****following definitions can not be used outside this driver******/
/* macro definition */
#define LED_CTRL_NUM                1u

#define LED_CTRL_MODEL_NOR          0u
#define LED_CTRL_MODEL_FLASH        1u
#define LED_CTRL_MODEL_BREATH       2u
#define LED_CTRL_MODEL_PWM          3u

/* Port and pin config for every LED */ 
#if (LED_CTRL_NUM >= 1)
#define LED_CTRL_PIN_0              SYS_CFG_PIN_CTRL_LED_BLUE 
#define LED_CTRL_PORT_0             0 
#endif
#if (LED_CTRL_NUM >= 2)
#define LED_CTRL_PIN_1              SYS_CFG_PIN_CTRL_LED_RED 
#define LED_CTRL_PORT_1             0 
#endif
#if (LED_CTRL_NUM >= 3)
#define LED_CTRL_PIN_2              SYS_CFG_PIN_LED_2
#define LED_CTRL_PORT_2             0
#endif
#if (LED_CTRL_NUM >= 4)
#define LED_CTRL_PIN_3              SYS_CFG_PIN_LED_3
#define LED_CTRL_PORT_3             0
#endif

 #define PIN_PORT_LED(_pin) (((_pin) >= P0_PIN_NUM) ? 1 : 0)

/* data type definiton  */
typedef struct
{
    uint32_t                    m_PortAddr;    /* Pointer to port data register */
    uint32_t                    m_PinOffset;   /* Offset of IO pin              */
}LedHW_Str;

/* const variable definiton  */
static const LedHW_Str gcLedHwCfg_t[LED_CTRL_NUM] =
{
#if (LED_CTRL_NUM >= 1)
    {
        LED_CTRL_PORT_0,
        LED_CTRL_PIN_0,
    }
#endif
#if (LED_CTRL_NUM >= 2)
    ,{
        LED_CTRL_PORT_1,
        LED_CTRL_PIN_1,
    }
#endif
#if (LED_CTRL_NUM >= 3)
    ,{
        LED_CTRL_PORT_2,
        LED_CTRL_PIN_2,
    }
#endif
#if (LED_CTRL_NUM >= 4)
    ,{
        LED_CTRL_PORT_3,
        LED_CTRL_PIN_3
    }
#endif
};

/* function configuration definition */
#define LED_CTRL_NOR_OFF(port,pin)           nrf_gpio_pin_clear(pin)
#define LED_CTRL_NOR_ON(port,pin)         	 nrf_gpio_pin_set(pin)

#endif /* End of header file */
