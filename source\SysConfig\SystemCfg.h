/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : SystemCfg.h
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/03/15    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/
#ifndef  __SYSTEM_CFG_H__
#define  __SYSTEM_CFG_H__

#include "Common.h"

#ifdef DEBUG_TEST_MSG_EN
    #define DEBUG_TEST_PIN_EN
    #define WDT_ENABLE                  FUN_EN
    #define APP_BOOT_ENABLE             FUN_EN 
    #define MCO2_CLK_OUT_ENABLE         FUN_DIS 
    #define FRAME_RATE_STATISTICS       FUN_DIS
    #define FPGA_ALG_DATA_CHECK         FUN_DIS
    #define DEBUG_PRINTF(...)           NRF_LOG_INFO( __VA_ARGS__)
#else
    #define WDT_ENABLE                  FUN_EN
    #define APP_BOOT_ENABLE             FUN_EN
    #define MCO2_CLK_OUT_ENABLE         FUN_DIS
    #define FRAME_RATE_STATISTICS       FUN_DIS
    #define FPGA_ALG_DATA_CHECK         FUN_DIS
    #define DEBUG_PRINTF(...)                
#endif

#ifdef DEBUG_TEST_PIN_EN
    #define DEBUG_TEST_POINT_PORT               
    #ifdef MIRAGE2B_BOARD
        #define DEBUG_TEST_POINT_PIN                0XFF
    #else
        #define DEBUG_TEST_POINT_PIN                0XFF         //short gun
    #endif
    #define TEST(pinLevel)                      nrf_gpio_pin_write(DEBUG_TEST_POINT_PIN, pinLevel)
    #define TEST_TOGGLE()                       nrf_gpio_pin_toggle(DEBUG_TEST_POINT_PIN)
    #define TEST_PIN_HIGH()                     nrf_gpio_pin_set(DEBUG_TEST_POINT_PIN)
    #define TEST_PIN_LOW()                      nrf_gpio_pin_clear(DEBUG_TEST_POINT_PIN)
#else
    #define TEST(pinLevel)
    #define TEST_TOGGLE()
    #define TEST_PIN_HIGH()
    #define TEST_PIN_LOW()
#endif
/*******************************************SYSTEM FUNC CONFIG******************************************************/
    #define SIRENX_NECK_RING_BOARD			FUN_DIS
    #define FUNC_BS83B04_TP					FUN_DIS
    #define FUNC_RT6010_MOTOR				FUN_DIS
    #define FUNC_HX3605_SPO2_HR				FUN_DIS
    #define FUNC_BLE_WHITE					FUN_EN
    #define FUNC_PN532_NFC					FUN_DIS
    #define FUNC_ELECTRIC_SHOCK				FUN_DIS

    #define IMU_CAPTURE_TIME_INTERVAL		                10000u		//1000000 / 10000u = 100Hz  
    #define IMU_REPORT_TIME_INTERVAL		                ((1000000U/IMU_CAPTURE_TIME_INTERVAL)/100U)		//100Hz
/*******************************************GPIO config******************************************************/
    /*uart config*/
    #define SYS_CFG_PIN_UART_TX                             NRF_GPIO_PIN_MAP(1, 13)
    #define SYS_CFG_PIN_UART_RX                             NRF_GPIO_PIN_MAP(0, 2)
    #define SYS_CFG_PIN_UART_CTS                            0xFF
    #define SYS_CFG_PIN_UART_RTS                            0xFF
    #define SYS_CFG_UART_HWFC                               APP_UART_FLOW_CONTROL_DISABLED
    #define SYS_CFG_UART_BAUD                               UART_BAUDRATE_BAUDRATE_Baud115200

    /*indicator led config*/
    #define SYS_CFG_PIN_CTRL_LED_BLUE                       NRF_GPIO_PIN_MAP(1, 10)
    // #define SYS_CFG_PIN_CTRL_LED_RED                        0xFF

    //IMU sensor config                 
    #define SYS_CFG_PIN_IMU_INT                             0xFF
    #define SYS_CFG_PIN_IMU_PULLDOWN_CFG                    NRF_GPIO_PIN_PULLUP
    #define SYS_CFG_PIN_IMU_DETECT_SIGNAL                   NRF_GPIOTE_POLARITY_HITOLO
    #define SYS_CFG_PIN_IMU_CS                              0xFF
    #define SYS_CFG_PIN_IMU_CLK                             0xFF
    #define SYS_CFG_PIN_IMU_MISO                            0xFF
    #define SYS_CFG_PIN_IMU_MOSI                            0xFF

    //button&Key
    #define SYS_CFG_PIN_BUTTON_POWER                        NRF_GPIO_PIN_MAP(1, 9) //power btn
    //MOTO 
    // #define SYS_CFG_PIN_MOTOR                              	16	

    //POWER control config
    // #define SYS_CFG_PIN_CTRL_POWER_IMU_EN                   9    //imu
    // #define SYS_CFG_PIN_CTRL_3V3_EN1                        NRF_GPIO_PIN_MAP(1, 6)
    // #define SYS_CFG_PIN_CTRL_3V3_EN2                        NRF_GPIO_PIN_MAP(1, 4)
    // #define SYS_CFG_PIN_CTRL_3V3_EN3                        NRF_GPIO_PIN_MAP(1, 2)
    #define SYS_CFG_PIN_CTRL_POWER_DCDC_PS                  22

    #define SYS_CFG_BAT_DETECT_ADC_CHANNEL                  NRF_SAADC_INPUT_AIN3
    #define SYS_CFG_BAT_DETECT_ADC_NUM                      3


    #define SYS_CFG_PIN_CTRL_POWER_CHG_CE                  0xFF
    #define SYS_CFG_PIN_CTRL_POWER_CHG_PG                  0xFF
    #define SYS_CFG_PIN_CTRL_POWER_CHG_DET                 0xFF

#endif   /* end of header file */
